{"name": "server", "version": "1.0.0", "description": "buits_sufian", "main": "index.ts", "type": "module", "scripts": {"dev": "nodemon --exec \"npx tsx\" index.ts", "start": "npx tsx index.ts", "build": "tsc", "postinstall": "npx prisma generate", "seed": "npx tsx prisma/seed.ts", "manage-subs": "node scripts/manage-subscriptions.js", "quick-subs": "node scripts/quick-sub-manager.js", "test-subs": "node scripts/test-subscription-manager.js", "db:push": "npx prisma db push", "db:setup": "npm run db:push && npm run seed"}, "author": "<PERSON> <PERSON>", "license": "ISC", "dependencies": {"@prisma/client": "^6.8.2", "@types/mqtt": "^0.0.34", "@types/qrcode": "^1.5.5", "bcrypt": "^5.1.1", "cors": "^2.8.5", "cron": "^4.3.1", "dotenv": "^16.5.0", "expo-server-sdk": "^3.15.0", "express": "^5.1.0", "jsonwebtoken": "^9.0.2", "mqtt": "^5.13.1", "nodemailer": "^6.9.8", "prisma": "^6.8.2", "qrcode": "^1.5.4"}, "devDependencies": {"@types/bcrypt": "^5.0.2", "@types/cors": "^2.8.19", "@types/cron": "^2.0.1", "@types/express": "^5.0.3", "@types/jsonwebtoken": "^9.0.10", "@types/node": "^24.0.3", "@types/nodemailer": "^6.4.14", "nodemon": "^3.1.10", "tsx": "^4.20.3", "typescript": "^5.8.3"}}