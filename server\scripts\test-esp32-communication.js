#!/usr/bin/env node

/**
 * ESP32 Communication Test Script
 * 
 * This script tests the complete communication flow between the server and ESP32:
 * 1. Connects to HiveMQ Cloud with authentication
 * 2. Subscribes to ESP32 status updates
 * 3. Sends start/stop commands to ESP32
 * 4. Monitors responses and timing
 */

import mqtt from 'mqtt';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Configuration
const MQTT_BROKER = process.env.MQTT_BROKER || 'mqtts://4f71cefb95804d629f86f0389c391427.s1.eu.hivemq.cloud:8883';
const MQTT_USERNAME = process.env.MQTT_USERNAME || 'abu_sufian';
const MQTT_PASSWORD = process.env.MQTT_PASSWORD || 'Grameenphne1400';
const MACHINE_ID = 'WASHER-001';

// MQTT Topics
const CONTROL_TOPIC = `washer/${MACHINE_ID}/control`;
const STATUS_TOPIC = `washer/${MACHINE_ID}/status`;
const DISPLAY_TOPIC = `washer/${MACHINE_ID}/display`;

console.log('🧪 ESP32 COMMUNICATION TEST');
console.log('═'.repeat(50));
console.log(`📡 Broker: ${MQTT_BROKER}`);
console.log(`🔐 Username: ${MQTT_USERNAME}`);
console.log(`🏭 Machine ID: ${MACHINE_ID}`);
console.log('═'.repeat(50));

// Test state
let testStep = 0;
let testResults = [];
let esp32Connected = false;
let cycleRunning = false;
let startTime = Date.now();

// Connect to MQTT broker
console.log('\n🔄 Step 1: Connecting to MQTT broker...');
const client = mqtt.connect(MQTT_BROKER, {
  username: MQTT_USERNAME,
  password: MQTT_PASSWORD,
  clientId: `esp32-test-${Date.now()}`,
  clean: true,
  keepalive: 15,
});

client.on('connect', () => {
  console.log('✅ Connected to MQTT broker');
  testResults.push({ step: 1, name: 'MQTT Connection', status: 'PASS' });
  
  // Subscribe to status topic
  console.log('\n🔄 Step 2: Subscribing to ESP32 status...');
  client.subscribe(STATUS_TOPIC, { qos: 1 }, (err) => {
    if (err) {
      console.error('❌ Failed to subscribe:', err);
      testResults.push({ step: 2, name: 'Status Subscription', status: 'FAIL', error: err.message });
    } else {
      console.log(`✅ Subscribed to: ${STATUS_TOPIC}`);
      testResults.push({ step: 2, name: 'Status Subscription', status: 'PASS' });
      
      // Wait for ESP32 to send initial status
      console.log('\n⏳ Step 3: Waiting for ESP32 initial status (10 seconds)...');
      setTimeout(() => {
        if (!esp32Connected) {
          console.log('⚠️ No status received from ESP32 - it may be offline');
          testResults.push({ step: 3, name: 'ESP32 Initial Status', status: 'TIMEOUT' });
        }
        startCycleTest();
      }, 10000);
    }
  });
});

client.on('message', (topic, message) => {
  const timestamp = new Date().toISOString();
  console.log(`\n📨 [${timestamp}] Message received:`);
  console.log(`   Topic: ${topic}`);
  console.log(`   Message: ${message.toString()}`);
  
  if (topic === STATUS_TOPIC) {
    try {
      const status = JSON.parse(message.toString());
      
      if (!esp32Connected && status.status) {
        esp32Connected = true;
        console.log('✅ ESP32 is online and responding!');
        testResults.push({ step: 3, name: 'ESP32 Initial Status', status: 'PASS', data: status });
      }
      
      // Check cycle status
      if (status.status === 'running' && !cycleRunning) {
        cycleRunning = true;
        console.log('✅ Cycle started successfully!');
        testResults.push({ step: 4, name: 'Start Cycle Command', status: 'PASS' });
        
        // Schedule stop test
        setTimeout(() => {
          stopCycleTest();
        }, 15000); // Stop after 15 seconds
        
      } else if (status.status === 'idle' && cycleRunning) {
        cycleRunning = false;
        console.log('✅ Cycle stopped successfully!');
        testResults.push({ step: 5, name: 'Stop Cycle Command', status: 'PASS' });
        
        // Complete tests
        setTimeout(() => {
          completeTests();
        }, 2000);
      }
      
      // Display status details
      if (status.cycle_elapsed) {
        console.log(`   ⏱️ Cycle elapsed: ${status.cycle_elapsed} seconds`);
      }
      if (status.wifi_rssi) {
        console.log(`   📶 WiFi signal: ${status.wifi_rssi} dBm`);
      }
      
    } catch (e) {
      console.log('   ℹ️ Non-JSON status message');
    }
  }
});

client.on('error', (error) => {
  console.error('❌ MQTT connection error:', error);
  testResults.push({ step: 1, name: 'MQTT Connection', status: 'FAIL', error: error.message });
});

client.on('close', () => {
  console.log('🔌 MQTT connection closed');
});

function startCycleTest() {
  if (!esp32Connected) {
    console.log('\n⚠️ Skipping cycle tests - ESP32 not responding');
    completeTests();
    return;
  }
  
  console.log('\n🔄 Step 4: Testing START cycle command...');
  
  // Create start command with slot end time (30 minutes from now)
  const slotEndTime = new Date(Date.now() + 30 * 60 * 1000).toISOString();
  const startCommand = {
    action: 'start',
    slotEndTime: slotEndTime,
    maxDuration: 30 * 60 // 30 minutes in seconds
  };
  
  console.log(`📤 Sending start command: ${JSON.stringify(startCommand)}`);
  
  client.publish(CONTROL_TOPIC, JSON.stringify(startCommand), { qos: 1 }, (err) => {
    if (err) {
      console.error('❌ Failed to send start command:', err);
      testResults.push({ step: 4, name: 'Start Cycle Command', status: 'FAIL', error: err.message });
      completeTests();
    } else {
      console.log('✅ Start command sent successfully');
      
      // Wait for response
      setTimeout(() => {
        if (!cycleRunning) {
          console.log('⚠️ ESP32 did not start cycle - check hardware');
          testResults.push({ step: 4, name: 'Start Cycle Command', status: 'TIMEOUT' });
          completeTests();
        }
      }, 10000);
    }
  });
}

function stopCycleTest() {
  console.log('\n🔄 Step 5: Testing STOP cycle command...');
  
  const stopCommand = {
    action: 'stop'
  };
  
  console.log(`📤 Sending stop command: ${JSON.stringify(stopCommand)}`);
  
  client.publish(CONTROL_TOPIC, JSON.stringify(stopCommand), { qos: 1 }, (err) => {
    if (err) {
      console.error('❌ Failed to send stop command:', err);
      testResults.push({ step: 5, name: 'Stop Cycle Command', status: 'FAIL', error: err.message });
      completeTests();
    } else {
      console.log('✅ Stop command sent successfully');
      
      // Wait for response
      setTimeout(() => {
        if (cycleRunning) {
          console.log('⚠️ ESP32 did not stop cycle - check hardware');
          testResults.push({ step: 5, name: 'Stop Cycle Command', status: 'TIMEOUT' });
        }
        completeTests();
      }, 10000);
    }
  });
}

function completeTests() {
  const totalTime = ((Date.now() - startTime) / 1000).toFixed(1);
  
  console.log('\n' + '═'.repeat(50));
  console.log('📊 TEST RESULTS SUMMARY');
  console.log('═'.repeat(50));
  
  let passCount = 0;
  let failCount = 0;
  
  testResults.forEach((result, index) => {
    const status = result.status === 'PASS' ? '✅' : 
                   result.status === 'FAIL' ? '❌' : '⚠️';
    console.log(`${status} Step ${result.step}: ${result.name} - ${result.status}`);
    
    if (result.error) {
      console.log(`   Error: ${result.error}`);
    }
    
    if (result.status === 'PASS') passCount++;
    else if (result.status === 'FAIL') failCount++;
  });
  
  console.log('─'.repeat(50));
  console.log(`📈 Results: ${passCount} passed, ${failCount} failed, ${testResults.length - passCount - failCount} warnings`);
  console.log(`⏱️ Total time: ${totalTime} seconds`);
  
  if (failCount === 0 && passCount >= 3) {
    console.log('🎉 ALL CRITICAL TESTS PASSED! ESP32 is ready for production.');
  } else {
    console.log('⚠️ Some tests failed. Check ESP32 hardware and connections.');
  }
  
  console.log('═'.repeat(50));
  
  // Disconnect and exit
  client.end();
  process.exit(failCount > 0 ? 1 : 0);
}

// Handle Ctrl+C
process.on('SIGINT', () => {
  console.log('\n🛑 Test interrupted by user');
  client.end();
  process.exit(1);
});

// Timeout after 2 minutes
setTimeout(() => {
  console.log('\n⏰ Test timeout reached (2 minutes)');
  completeTests();
}, 120000);
