# Learn more https://docs.github.com/en/get-started/getting-started-with-git/ignoring-files

# dependencies
node_modules/

# Expo
.expo/
dist/
web-build/
expo-env.d.ts

# Native
.kotlin/
*.orig.*
*.jks
*.p8
*.p12
*.key
*.mobileprovision

# Metro
.metro-health-check*

# debug
npm-debug.*
yarn-debug.*
yarn-error.*

# macOS
.DS_Store
*.pem

# local env files
.env*.local

# typescript
*.tsbuildinfo

app-example

# Native generated folders (expo prebuild)
ios/

# To allow EAS Build to access google-services.json, we need to adjust the ignore rules.
# We ignore the contents of the android directory, but not the directory itself.
# Then we can successfully un-ignore the path to the google-services.json file.

android/*
!android/app/
android/app/*
!android/app/google-services.json
