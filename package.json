{"name": "smartbulogin", "main": "expo-router/entry", "version": "1.0.0", "private": true, "scripts": {"start": "expo start --dev-client", "reset-project": "node ./scripts/reset-project.js", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web", "lint": "expo lint"}, "dependencies": {"@expo/vector-icons": "^14.1.0", "@react-native-async-storage/async-storage": "2.1.2", "@react-native-google-signin/google-signin": "^14.0.1", "@react-native-masked-view/masked-view": "0.3.2", "@react-navigation/bottom-tabs": "^7.3.10", "@react-navigation/elements": "^2.3.8", "@react-navigation/native": "^7.1.6", "@tanstack/react-query": "^5.80.10", "@tanstack/react-query-devtools": "^5.80.10", "axios": "^1.9.0", "expo": "53.0.12", "expo-auth-session": "~6.2.0", "expo-blur": "~14.1.5", "expo-camera": "~16.1.8", "expo-constants": "~17.1.6", "expo-dev-client": "~5.2.1", "expo-font": "~13.3.1", "expo-haptics": "~14.1.4", "expo-image": "~2.3.0", "expo-jwt": "^1.8.2", "expo-linear-gradient": "~14.1.5", "expo-linking": "~7.1.5", "expo-router": "~5.1.0", "expo-secure-store": "~14.2.3", "expo-splash-screen": "~0.30.9", "expo-status-bar": "~2.2.3", "expo-symbols": "~0.4.5", "expo-system-ui": "~5.0.9", "expo-web-browser": "~14.2.0", "moment": "^2.30.1", "mongodb": "^6.17.0", "moti": "^0.30.0", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.4", "react-native-gesture-handler": "~2.24.0", "react-native-iap": "^12.16.2", "react-native-reanimated": "~3.17.4", "react-native-redash": "^18.1.3", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-size-matters": "^0.4.2", "react-native-svg": "15.11.2", "react-native-swiper": "^1.6.0", "react-native-web": "~0.20.0", "react-native-webview": "13.13.5", "expo-notifications": "~0.31.3", "expo-device": "~7.1.4"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~19.0.10", "eslint": "^9.25.0", "eslint-config-expo": "~9.2.0", "typescript": "~5.8.3"}, "expo": {"doctor": {"reactNativeDirectoryCheck": {"exclude": ["react-native-iap", "react-native-swiper", "moment", "mongodb"], "listUnknownPackages": false}}}, "eas": {"build": {"development": {"buildHooks": {"preInstall": "echo $GOOGLE_SERVICES_BASE64 | base64 --decode > android/app/google-services.json"}}, "production": {"buildHooks": {"preInstall": "echo $GOOGLE_SERVICES_BASE64 | base64 --decode > android/app/google-services.json"}}}}}