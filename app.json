{"expo": {"name": "smartbulogin", "slug": "smartbulogin", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "smartbulogin", "userInterfaceStyle": "automatic", "newArchEnabled": false, "ios": {"supportsTablet": true, "bundleIdentifier": "com.sufian.smartBU"}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/adaptive-icon.png", "backgroundColor": "#ffffff"}, "package": "com.sufian.smartBU", "edgeToEdgeEnabled": true, "googleServicesFile": "./android/app/google-services.json"}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/favicon.png"}, "plugins": ["expo-router", "expo-notifications", ["expo-splash-screen", {"image": "./assets/images/splash-icon.png", "imageWidth": 200, "resizeMode": "contain", "backgroundColor": "#ffffff"}], "expo-secure-store", ["@react-native-google-signin/google-signin", {"iosUrlScheme": "com.googleusercontent.apps.115355708216-kkavg7bmm4pdr58d8b769hhhsu3f6sgm"}], "expo-web-browser", "react-native-iap", ["expo-camera", {"cameraPermission": "Allow $(PRODUCT_NAME) to access your camera", "microphonePermission": "Allow $(PRODUCT_NAME) to access your microphone", "recordAudioAndroid": false}]], "experiments": {"typedRoutes": true}, "extra": {"router": {}, "eas": {"projectId": "1e2ad825-0fa8-493b-b8e3-210235cf67c1"}}}}