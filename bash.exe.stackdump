Stack trace:
Frame         Function      Args
0007FFFF9F20  00021005FEBA (000210285F48, 00021026AB6E, 000000000000, 0007FFFF8E20) msys-2.0.dll+0x1FEBA
0007FFFF9F20  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFA1F8) msys-2.0.dll+0x67F9
0007FFFF9F20  000210046832 (000210285FF9, 0007FFFF9DD8, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFF9F20  000210068F86 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28F86
0007FFFF9F20  0002100690B4 (0007FFFF9F30, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x290B4
0007FFFFA200  00021006A49D (0007FFFF9F30, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A49D
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFD5DA80000 ntdll.dll
7FFD5CBE0000 KERNEL32.DLL
7FFD5B0E0000 KERNELBASE.dll
7FFD5D4A0000 USER32.dll
7FFD5AF60000 win32u.dll
7FFD5D470000 GDI32.dll
000210040000 msys-2.0.dll
7FFD5B6E0000 gdi32full.dll
7FFD5AEB0000 msvcp_win.dll
7FFD5AF90000 ucrtbase.dll
7FFD5D7C0000 advapi32.dll
7FFD5D2F0000 msvcrt.dll
7FFD5D910000 sechost.dll
7FFD5BDE0000 RPCRT4.dll
7FFD5A2F0000 CRYPTBASE.DLL
7FFD5AE10000 bcryptPrimitives.dll
7FFD5BDA0000 IMM32.DLL
