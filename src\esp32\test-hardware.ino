/*
 * ESP32 Hardware Test for Washing Machine Controller
 * 
 * This test script verifies:
 * 1. WiFi connection
 * 2. MQTT connection with HiveMQ Cloud (TLS + Auth)
 * 3. Relay control functionality
 * 4. LED indicators
 * 5. Time synchronization
 * 
 * Upload this to your ESP32 first to test all components
 */

#include <WiFi.h>
#include <WiFiClientSecure.h>
#include <PubSubClient.h>
#include <time.h>

// WiFi Configuration - UPDATE WITH YOUR CREDENTIALS
const char* ssid = "TP";
const char* password = "12233344";

// MQTT Configuration - HiveMQ Cloud
const char* mqtt_server = "4f71cefb95804d629f86f0389c391427.s1.eu.hivemq.cloud";
const int mqtt_port = 8883;
const char* mqtt_username = "abu_sufian";
const char* mqtt_password = "Grameenphne1400";

// Hardware pins
const int RELAY_PIN = 4;  // GPIO 4 for relay
const int LED_PIN = 2;    // Built-in LED

// MQTT client
WiFiClientSecure espClient;
PubSubClient client(espClient);

// Test variables
bool testPassed = true;
int testStep = 0;

void setup() {
  Serial.begin(115200);
  delay(2000);
  
  Serial.println("\n" + String("=").repeat(50));
  Serial.println("🧪 ESP32 HARDWARE TEST - WASHING MACHINE CONTROLLER");
  Serial.println("=".repeat(50));
  
  // Initialize pins
  pinMode(RELAY_PIN, OUTPUT);
  pinMode(LED_PIN, OUTPUT);
  digitalWrite(RELAY_PIN, HIGH);  // Relay OFF (Active LOW)
  digitalWrite(LED_PIN, LOW);     // LED OFF
  
  runTests();
}

void loop() {
  // Keep MQTT connection alive during tests
  if (client.connected()) {
    client.loop();
  }
  delay(1000);
}

void runTests() {
  Serial.println("🚀 Starting hardware tests...\n");
  
  // Test 1: GPIO Test
  testStep = 1;
  Serial.println("📌 TEST 1: GPIO Functionality");
  testGPIO();
  
  // Test 2: WiFi Connection
  testStep = 2;
  Serial.println("\n📶 TEST 2: WiFi Connection");
  testWiFi();
  
  // Test 3: Time Synchronization
  testStep = 3;
  Serial.println("\n⏰ TEST 3: Time Synchronization");
  testTimeSync();
  
  // Test 4: MQTT Connection
  testStep = 4;
  Serial.println("\n📡 TEST 4: MQTT Connection (HiveMQ Cloud)");
  testMQTT();
  
  // Test 5: MQTT Communication
  testStep = 5;
  Serial.println("\n💬 TEST 5: MQTT Communication");
  testMQTTCommunication();
  
  // Final Results
  Serial.println("\n" + String("=").repeat(50));
  if (testPassed) {
    Serial.println("✅ ALL TESTS PASSED! Hardware is ready for production.");
    blinkSuccess();
  } else {
    Serial.println("❌ SOME TESTS FAILED! Check the issues above.");
    blinkError();
  }
  Serial.println("=".repeat(50));
}

void testGPIO() {
  Serial.println("   Testing relay and LED control...");
  
  // Test LED
  Serial.println("   • Testing LED (3 blinks)");
  for (int i = 0; i < 3; i++) {
    digitalWrite(LED_PIN, HIGH);
    delay(300);
    digitalWrite(LED_PIN, LOW);
    delay(300);
  }
  
  // Test Relay
  Serial.println("   • Testing relay (ON for 2 seconds)");
  digitalWrite(RELAY_PIN, LOW);   // Relay ON (Active LOW)
  digitalWrite(LED_PIN, HIGH);    // LED ON to indicate relay state
  delay(2000);
  digitalWrite(RELAY_PIN, HIGH);  // Relay OFF
  digitalWrite(LED_PIN, LOW);     // LED OFF
  
  Serial.println("   ✅ GPIO test completed");
}

void testWiFi() {
  Serial.printf("   Connecting to WiFi: %s\n", ssid);
  WiFi.begin(ssid, password);
  
  int attempts = 0;
  while (WiFi.status() != WL_CONNECTED && attempts < 20) {
    delay(500);
    Serial.print(".");
    attempts++;
  }
  
  if (WiFi.status() == WL_CONNECTED) {
    Serial.println();
    Serial.printf("   ✅ WiFi connected successfully!\n");
    Serial.printf("   • IP Address: %s\n", WiFi.localIP().toString().c_str());
    Serial.printf("   • Signal Strength: %d dBm\n", WiFi.RSSI());
    Serial.printf("   • MAC Address: %s\n", WiFi.macAddress().c_str());
  } else {
    Serial.println();
    Serial.println("   ❌ WiFi connection failed!");
    testPassed = false;
  }
}

void testTimeSync() {
  Serial.println("   Synchronizing time with NTP server...");
  configTime(0, 0, "pool.ntp.org");
  
  time_t now = time(nullptr);
  int attempts = 0;
  while (now < 8 * 3600 * 2 && attempts < 15) {
    delay(1000);
    Serial.print(".");
    now = time(nullptr);
    attempts++;
  }
  
  if (now > 8 * 3600 * 2) {
    Serial.println();
    Serial.printf("   ✅ Time synchronized: %s", ctime(&now));
  } else {
    Serial.println();
    Serial.println("   ⚠️ Time sync timeout - may affect TLS");
  }
}

void testMQTT() {
  Serial.printf("   Connecting to MQTT broker: %s:%d\n", mqtt_server, mqtt_port);
  
  // Configure TLS
  espClient.setInsecure();  // Skip cert verification for testing
  client.setServer(mqtt_server, mqtt_port);
  
  // Try to connect with authentication
  String clientId = "ESP32_Test_" + String(millis());
  
  Serial.println("   • Attempting secure connection with authentication...");
  
  if (client.connect(clientId.c_str(), mqtt_username, mqtt_password)) {
    Serial.println("   ✅ MQTT connected successfully!");
    Serial.printf("   • Client ID: %s\n", clientId.c_str());
    Serial.printf("   • Username: %s\n", mqtt_username);
    Serial.println("   • TLS: Enabled");
    
    // Test subscription
    if (client.subscribe("test/esp32/ping")) {
      Serial.println("   • ✅ Test subscription successful");
    } else {
      Serial.println("   • ❌ Test subscription failed");
      testPassed = false;
    }
    
  } else {
    Serial.printf("   ❌ MQTT connection failed (error code: %d)\n", client.state());
    Serial.println("   • Check credentials and network connectivity");
    testPassed = false;
  }
}

void testMQTTCommunication() {
  if (!client.connected()) {
    Serial.println("   ⚠️ Skipping - MQTT not connected");
    return;
  }
  
  Serial.println("   Testing MQTT publish/subscribe...");
  
  // Publish test message
  String testMessage = "Hello from ESP32 at " + String(millis());
  if (client.publish("test/esp32/status", testMessage.c_str())) {
    Serial.println("   ✅ Test message published successfully");
  } else {
    Serial.println("   ❌ Failed to publish test message");
    testPassed = false;
  }
  
  // Test washer-specific topics
  if (client.subscribe("washer/WASHER-001/control")) {
    Serial.println("   ✅ Subscribed to control topic");
  } else {
    Serial.println("   ❌ Failed to subscribe to control topic");
    testPassed = false;
  }
  
  // Publish status
  String statusMsg = "{\"status\":\"test\",\"machine_id\":\"WASHER-001\"}";
  if (client.publish("washer/WASHER-001/status", statusMsg.c_str())) {
    Serial.println("   ✅ Status message published");
  } else {
    Serial.println("   ❌ Failed to publish status");
    testPassed = false;
  }
}

void blinkSuccess() {
  // Green pattern: 5 quick blinks
  for (int i = 0; i < 5; i++) {
    digitalWrite(LED_PIN, HIGH);
    delay(150);
    digitalWrite(LED_PIN, LOW);
    delay(150);
  }
}

void blinkError() {
  // Red pattern: 3 long blinks
  for (int i = 0; i < 3; i++) {
    digitalWrite(LED_PIN, HIGH);
    delay(500);
    digitalWrite(LED_PIN, LOW);
    delay(500);
  }
}
