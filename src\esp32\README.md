# ESP32 Washing Machine Controller - Hardware Testing Guide

## 🔧 Hardware Setup

### Required Components
- **ESP32 Development Board** (ESP32-WROOM-32 or similar)
- **Relay Module** (5V or 3.3V compatible, Active LOW)
- **Jumper Wires**
- **Breadboard** (optional)
- **Washing Machine** or **Test Load** (LED/Buzzer for testing)

### Wiring Diagram
```
ESP32 Pin    →    Component
GPIO 4       →    Relay IN (Control Signal)
GPIO 2       →    Built-in LED (Status Indicator)
3.3V/5V      →    Relay VCC
GND          →    Relay GND

Relay Output →    Washing Machine Control Circuit
```

### Safety Notes ⚠️
- **NEVER work with live AC power without proper electrical knowledge**
- **Use proper isolation and safety switches**
- **Test with low voltage loads first (12V DC max)**
- **Consider using a qualified electrician for AC connections**

## 🧪 Testing Procedure

### Step 1: Upload Test Firmware
1. Open `test-hardware.ino` in Arduino IDE
2. Update WiFi credentials:
   ```cpp
   const char* ssid = "YOUR_WIFI_SSID";
   const char* password = "YOUR_WIFI_PASSWORD";
   ```
3. Upload to ESP32
4. Open Serial Monitor (115200 baud)
5. Watch test results

### Step 2: Verify Test Results
The test will check:
- ✅ GPIO functionality (LED and relay)
- ✅ WiFi connection
- ✅ Time synchronization
- ✅ MQTT connection to HiveMQ Cloud
- ✅ MQTT communication

### Step 3: Upload Production Firmware
1. If all tests pass, upload `washer.ino`
2. Monitor serial output for connection status
3. Test MQTT commands from server

## 📡 MQTT Configuration

### Current Settings (HiveMQ Cloud)
- **Broker**: `4f71cefb95804d629f86f0389c391427.s1.eu.hivemq.cloud`
- **Port**: `8883` (TLS/SSL)
- **Username**: `abu_sufian`
- **Password**: `Grameenphne1400`

### MQTT Topics
- **Control**: `washer/WASHER-001/control` (subscribe)
- **Status**: `washer/WASHER-001/status` (publish)
- **Display**: `washer/WASHER-001/display` (subscribe)

### Command Format
```json
{
  "action": "start",
  "slotEndTime": "2025-06-28T15:30:00.000Z",
  "maxDuration": 1800
}
```

## 🔍 Troubleshooting

### WiFi Issues
- Check SSID and password
- Ensure 2.4GHz network (ESP32 doesn't support 5GHz)
- Check signal strength (should be > -70 dBm)

### MQTT Connection Issues
- Verify internet connectivity
- Check MQTT credentials
- Ensure time is synchronized (required for TLS)
- Check firewall settings

### Relay Issues
- Verify wiring (GPIO 4 to Relay IN)
- Check relay type (Active LOW vs Active HIGH)
- Test with multimeter
- Ensure adequate power supply

### Common Error Codes
- **MQTT Error -2**: Network connection failed
- **MQTT Error -3**: Network connection lost
- **MQTT Error -4**: Connection timeout
- **MQTT Error -5**: Connection refused (bad credentials)

## 🧪 Testing Commands

### Using Server API
```bash
# Start cycle
curl -X POST http://your-server:3000/api/machines/WASHER-001/start \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"

# Stop cycle
curl -X POST http://your-server:3000/api/machines/WASHER-001/stop \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### Using MQTT Client (mosquitto_pub)
```bash
# Start cycle
mosquitto_pub -h 4f71cefb95804d629f86f0389c391427.s1.eu.hivemq.cloud \
  -p 8883 --capath /etc/ssl/certs/ \
  -u abu_sufian -P Grameenphne1400 \
  -t "washer/WASHER-001/control" \
  -m '{"action":"start","slotEndTime":"2025-06-28T16:00:00.000Z"}'

# Stop cycle
mosquitto_pub -h 4f71cefb95804d629f86f0389c391427.s1.eu.hivemq.cloud \
  -p 8883 --capath /etc/ssl/certs/ \
  -u abu_sufian -P Grameenphne1400 \
  -t "washer/WASHER-001/control" \
  -m '{"action":"stop"}'
```

## 📊 Expected Serial Output

### Successful Connection
```
=== ESP32 Washing Machine Controller ===
✅ WiFi connected successfully!
✅ Time synchronized
🔐 TLS configured for secure MQTT connection
✅ MQTT connected successfully!
📩 Subscribed to: washer/WASHER-001/control
📤 Status update sent: idle
```

### Cycle Operation
```
📨 MQTT message received:
   Topic: washer/WASHER-001/control
   Message: {"action":"start","slotEndTime":"2025-06-28T16:00:00.000Z"}
🚀 Starting washing cycle...
✅ Washing cycle started!
📤 Status update sent: running
```

## 🔄 Production Deployment

### Before Production
1. ✅ Complete all hardware tests
2. ✅ Verify MQTT communication
3. ✅ Test with actual washing machine (safely)
4. ✅ Install proper electrical protection
5. ✅ Document installation for maintenance

### Security Considerations
- Change default WiFi credentials
- Use strong MQTT passwords
- Consider certificate-based authentication
- Implement OTA update mechanism
- Add watchdog timer for reliability

## 📞 Support
If you encounter issues:
1. Check serial monitor output
2. Verify all connections
3. Test with simple loads first
4. Consult electrical professional for AC wiring
