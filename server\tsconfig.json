{"compilerOptions": {"target": "ES2020", "module": "ESNext", "moduleResolution": "node", "allowSyntheticDefaultImports": true, "esModuleInterop": true, "allowJs": true, "strict": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "outDir": "./dist", "rootDir": "./", "declaration": true, "declarationMap": true, "sourceMap": true}, "include": ["**/*.ts", "**/*.js"], "exclude": ["node_modules", "dist"], "extends": "expo/tsconfig.base"}