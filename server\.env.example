# Database
DATABASE_URL="mongodb://localhost:27017/laundryapp"

# JWT Secret Keys
JWT_SECRET_KEY="your-super-secret-jwt-key-here"
EXPO_PUBLIC_JWT_SECRET_KEY="your-expo-public-jwt-secret-key-here"

# Email Configuration (for OTP sending)
EMAIL_USER="abu49539@@gmail.com"
EMAIL_PASS="enoc lgus yfet tswg"

# MQTT Configuration (optional)
MQTT_ENABLED="true"
MQTT_BROKER="mqtt://localhost:1883"
MQTT_BROKER_URL="mqtt://localhost:1883"

# Server Configuration
PORT=3000
NODE_ENV="development"

# Stripe (if using payments)
STRIPE_SECRET_KEY="sk_test_..."
STRIPE_PUBLISHABLE_KEY="pk_test_..."
