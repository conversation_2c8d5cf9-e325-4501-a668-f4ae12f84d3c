generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "mongodb"
  url      = env("DATABASE_URL")
}

enum Role {
  User
  Admin
}

enum SlotStatus {
  Reserved
  Completed
  Cancelled
  Expired
}

enum MachineStatus {
  Available
  InUse
  Offline
}

enum NotificationStatus {
  Unread
  Read
}

enum TicketStatus {
  Pending
  Resolved
  Closed
}

model User {
  id               String            @id @default(auto()) @map("_id") @db.ObjectId
  name             String
  email            String            @unique
  password         String?           // For email/password login
  googleId         String?           // For Google Login
  phone_number     String?           // Changed to String for flexibility
  avatar           String?
  stripeCustomerId String?           // For Stripe payments
  role             Role              @default(User)
  pushToken        String?           // For push notifications
  verified         <PERSON>olean           @default(false)
  slots            Slot[]            // Booked slots
  notifications    Notification[]    // Received notifications
  tickets          Ticket[]          // Created tickets
  ticketReplies    TicketReply[]     // Replies to tickets
  usageLogs        UsageLog[]        // Usage history for auditing
  otps             Otp[]             // OTP records for verification
  createdAt        DateTime          @default(now())
  updatedAt        DateTime          @updatedAt
}

model Machine {
  id          String         @id @default(auto()) @map("_id") @db.ObjectId
  machineId   String         @unique // e.g., "washer1"
  qrCode      String         // QR code data (e.g., "machineId:washer1")
  status      MachineStatus  @default(Available)
  location    String?        // Optional, e.g., "Laundromat A"
  slots       Slot[]         // Slots booked for this machine
  usageLogs   UsageLog[]     // Usage history
  createdAt   DateTime       @default(now())
  updatedAt   DateTime       @updatedAt
}

model Slot {
  id            String         @id @default(auto()) @map("_id") @db.ObjectId
  userId        String         @db.ObjectId
  user          User           @relation(fields: [userId], references: [id])
  machineId     String         @db.ObjectId
  machine       Machine        @relation(fields: [machineId], references: [id])
  slotTime      DateTime       // Start time (e.g., 2025-06-17T10:00:00Z)
  duration      Int            // Duration in milliseconds (e.g., 1800000)
  authCode      String         // One-time auth code (e.g., "ABC123")
  status        SlotStatus     @default(Reserved)
  cycleEndTime  DateTime?      // When the cycle should end (for background job cleanup)
  notifications Notification[] // Related notifications
  usageLogs     UsageLog[]     // Usage history
  createdAt     DateTime       @default(now())
  updatedAt     DateTime       @updatedAt
}

model Notification {
  id            String              @id @default(auto()) @map("_id") @db.ObjectId
  title         String
  message       String
  status        NotificationStatus  @default(Unread)
  userId        String              @db.ObjectId
  user          User                @relation(fields: [userId], references: [id])
  slotId        String?             @db.ObjectId
  slot          Slot?               @relation(fields: [slotId], references: [id])
  ticketId      String?             @db.ObjectId
  ticket        Ticket?             @relation(fields: [ticketId], references: [id])
  redirect_link String?             // Optional link to slot or ticket
  createdAt     DateTime            @default(now())
  updatedAt     DateTime            @updatedAt
}

model Ticket {
  id          String        @id @default(auto()) @map("_id") @db.ObjectId
  creatorId   String        @db.ObjectId
  user        User          @relation(fields: [creatorId], references: [id])
  ticketTitle String
  details     String
  status      TicketStatus  @default(Pending)
  replies     TicketReply[] // Replies to the ticket
  createdAt   DateTime      @default(now())
  updatedAt   DateTime      @updatedAt
  notifications Notification[] // Related notifications
}

model TicketReply {
  id          String     @id @default(auto()) @map("_id") @db.ObjectId
  ticketId    String     @db.ObjectId
  ticket      Ticket     @relation(fields: [ticketId], references: [id])
  reply       String
  userId      String     @db.ObjectId
  user        User       @relation(fields: [userId], references: [id])
  createdAt   DateTime?  @default(now())
  updatedAt   DateTime?  @updatedAt
}

model UsageLog {
  id          String     @id @default(auto()) @map("_id") @db.ObjectId
  userId      String     @db.ObjectId
  user        User       @relation(fields: [userId], references: [id])
  machineId   String     @db.ObjectId
  machine     Machine    @relation(fields: [machineId], references: [id])
  slotId      String     @db.ObjectId
  slot        Slot       @relation(fields: [slotId], references: [id])
  action      String     // e.g., "Booked", "Started", "Completed", "Cancelled"
  createdAt   DateTime   @default(now())
}

model Otp {
  id        String   @id @default(auto()) @map("_id") @db.ObjectId
  email     String   // Email for which OTP was generated
  otp       String   // 4-digit OTP code
  userId    String?  @db.ObjectId // Optional, linked after verification
  user      User?    @relation(fields: [userId], references: [id])
  expiresAt DateTime // OTP expiration time (5 minutes)
  verified  Boolean  @default(false) // Whether OTP has been verified
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([email, expiresAt])
}